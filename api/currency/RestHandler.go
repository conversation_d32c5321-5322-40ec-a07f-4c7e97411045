/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package currency

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/devtron-labs/central-api/common"
	currencyPkg "github.com/devtron-labs/central-api/pkg/currency"
	"go.uber.org/zap"
)

// CurrencyRestHandler defines the interface for currency REST operations
type CurrencyRestHandler interface {
	// GetExchangeRates returns the latest exchange rates
	GetExchangeRates(w http.ResponseWriter, r *http.Request)

	// ConvertCurrency converts an amount from one currency to another
	ConvertCurrency(w http.ResponseWriter, r *http.Request)

	// ConvertCurrencyFromQuery converts currency using query parameters
	ConvertCurrencyFromQuery(w http.ResponseWriter, r *http.Request)

	// GetSupportedCurrencies returns a list of supported currencies
	GetSupportedCurrencies(w http.ResponseWriter, r *http.Request)

	// GetCacheStatus returns cache status information
	GetCacheStatus(w http.ResponseWriter, r *http.Request)

	// RefreshRates manually refreshes the exchange rates
	RefreshRates(w http.ResponseWriter, r *http.Request)
}

// CurrencyRestHandlerImpl implements the CurrencyRestHandler interface
type CurrencyRestHandlerImpl struct {
	logger          *zap.SugaredLogger
	currencyService currencyPkg.CurrencyService
}

// NewCurrencyRestHandlerImpl creates a new instance of CurrencyRestHandlerImpl
func NewCurrencyRestHandlerImpl(logger *zap.SugaredLogger, currencyService currencyPkg.CurrencyService) *CurrencyRestHandlerImpl {
	return &CurrencyRestHandlerImpl{
		logger:          logger,
		currencyService: currencyService,
	}
}

// GetExchangeRates handles GET /currency/rates requests
func (h *CurrencyRestHandlerImpl) GetExchangeRates(w http.ResponseWriter, r *http.Request) {
	// Get base currency from query parameter (optional)
	base := r.URL.Query().Get("base")
	if base != "" {
		base = strings.ToUpper(base)
		if !currencyPkg.ValidateCurrencyCode(base) {
			h.writeErrorResponse(w, http.StatusBadRequest, "Invalid base currency code")
			return
		}
	}

	rates, err := h.currencyService.GetExchangeRates(r.Context(), base)
	if err != nil {
		h.logger.Errorw("failed to get exchange rates", "base", base, "err", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve exchange rates")
		return
	}

	h.writeSuccessResponse(w, rates)
}

// ConvertCurrency handles POST /currency/convert requests
func (h *CurrencyRestHandlerImpl) ConvertCurrency(w http.ResponseWriter, r *http.Request) {
	var request currencyPkg.ConversionRequest

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate input
	if request.From == "" || request.To == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Both 'from' and 'to' currencies are required")
		return
	}

	request.From = strings.ToUpper(request.From)
	request.To = strings.ToUpper(request.To)

	if !currencyPkg.ValidateCurrencyCode(request.From) {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid 'from' currency code")
		return
	}

	if !currencyPkg.ValidateCurrencyCode(request.To) {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid 'to' currency code")
		return
	}

	if request.Amount < 0 {
		h.writeErrorResponse(w, http.StatusBadRequest, "Amount must be non-negative")
		return
	}

	result, err := h.currencyService.ConvertCurrency(r.Context(), &request)
	if err != nil {
		h.logger.Errorw("failed to convert currency", "request", request, "err", err)
		if strings.Contains(err.Error(), "not supported") {
			h.writeErrorResponse(w, http.StatusBadRequest, err.Error())
		} else {
			h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to convert currency")
		}
		return
	}

	// Format the result for better precision
	result.Result = currencyPkg.FormatCurrency(result.Result)
	result.Rate = currencyPkg.FormatCurrency(result.Rate)

	h.writeSuccessResponse(w, result)
}

// GetSupportedCurrencies handles GET /currency/currencies requests
func (h *CurrencyRestHandlerImpl) GetSupportedCurrencies(w http.ResponseWriter, r *http.Request) {
	currencies, err := h.currencyService.GetSupportedCurrencies(r.Context())
	if err != nil {
		h.logger.Errorw("failed to get supported currencies", "err", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve supported currencies")
		return
	}

	response := map[string]interface{}{
		"currencies": currencies,
		"count":      len(currencies),
		"common":     currencyPkg.GetCommonCurrencies(),
	}

	h.writeSuccessResponse(w, response)
}

// GetCacheStatus handles GET /currency/cache/status requests
func (h *CurrencyRestHandlerImpl) GetCacheStatus(w http.ResponseWriter, r *http.Request) {
	status := h.currencyService.GetCacheStatus()
	h.writeSuccessResponse(w, status)
}

// RefreshRates handles POST /currency/refresh requests
func (h *CurrencyRestHandlerImpl) RefreshRates(w http.ResponseWriter, r *http.Request) {
	err := h.currencyService.RefreshRates(r.Context())
	if err != nil {
		h.logger.Errorw("failed to refresh rates", "err", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to refresh exchange rates")
		return
	}

	response := map[string]interface{}{
		"message": "Exchange rates refreshed successfully",
		"status":  "success",
	}

	h.writeSuccessResponse(w, response)
}

// ConvertCurrencyFromQuery handles GET /currency/convert requests with query parameters
func (h *CurrencyRestHandlerImpl) ConvertCurrencyFromQuery(w http.ResponseWriter, r *http.Request) {
	from := strings.ToUpper(r.URL.Query().Get("from"))
	to := strings.ToUpper(r.URL.Query().Get("to"))
	amountStr := r.URL.Query().Get("amount")

	if from == "" || to == "" || amountStr == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Parameters 'from', 'to', and 'amount' are required")
		return
	}

	if !currencyPkg.ValidateCurrencyCode(from) {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid 'from' currency code")
		return
	}

	if !currencyPkg.ValidateCurrencyCode(to) {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid 'to' currency code")
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount < 0 {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid amount")
		return
	}

	request := &currencyPkg.ConversionRequest{
		From:   from,
		To:     to,
		Amount: amount,
	}

	result, err := h.currencyService.ConvertCurrency(r.Context(), request)
	if err != nil {
		h.logger.Errorw("failed to convert currency", "request", request, "err", err)
		if strings.Contains(err.Error(), "not supported") {
			h.writeErrorResponse(w, http.StatusBadRequest, err.Error())
		} else {
			h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to convert currency")
		}
		return
	}

	// Format the result for better precision
	result.Result = currencyPkg.FormatCurrency(result.Result)
	result.Rate = currencyPkg.FormatCurrency(result.Rate)

	h.writeSuccessResponse(w, result)
}

// writeSuccessResponse writes a successful JSON response
func (h *CurrencyRestHandlerImpl) writeSuccessResponse(w http.ResponseWriter, data interface{}) {
	response := common.Response{
		Code:   http.StatusOK,
		Status: "OK",
		Result: data,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.Errorw("failed to encode response", "err", err)
	}
}

// writeErrorResponse writes an error JSON response
func (h *CurrencyRestHandlerImpl) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := common.Response{
		Code:   statusCode,
		Status: "Error",
		Result: nil,
		Errors: []*common.ApiError{
			{
				Code:            "CURRENCY_ERROR",
				InternalMessage: message,
				UserMessage:     message,
			},
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.Errorw("failed to encode error response", "err", err)
	}
}
