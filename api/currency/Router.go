/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package currency

import (
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

// CurrencyRouter handles routing for currency-related endpoints
type CurrencyRouter struct {
	logger      *zap.SugaredLogger
	restHandler CurrencyRestHandler
}

// NewCurrencyRouter creates a new instance of CurrencyRouter
func NewCurrencyRouter(logger *zap.SugaredLogger, restHandler CurrencyRestHandler) *CurrencyRouter {
	return &CurrencyRouter{
		logger:      logger,
		restHandler: restHandler,
	}
}

// InitCurrencyRoutes initializes all currency-related routes
func (r *CurrencyRouter) InitCurrencyRoutes(router *mux.Router) {
	r.logger.Info("initializing currency routes")

	// Create a subrouter for currency endpoints
	currencyRouter := router.PathPrefix("/currency").Subrouter()

	// Exchange rates endpoints
	currencyRouter.Path("/rates").
		Methods("GET").
		HandlerFunc(r.restHandler.GetExchangeRates)

	// Currency conversion endpoints
	currencyRouter.Path("/convert").
		Methods("POST").
		HandlerFunc(r.restHandler.ConvertCurrency)

	currencyRouter.Path("/convert").
		Methods("GET").
		HandlerFunc(r.restHandler.ConvertCurrencyFromQuery)

	// Supported currencies endpoint
	currencyRouter.Path("/currencies").
		Methods("GET").
		HandlerFunc(r.restHandler.GetSupportedCurrencies)

	// Cache management endpoints
	currencyRouter.Path("/cache/status").
		Methods("GET").
		HandlerFunc(r.restHandler.GetCacheStatus)

	currencyRouter.Path("/refresh").
		Methods("POST").
		HandlerFunc(r.restHandler.RefreshRates)

	r.logger.Info("currency routes initialized successfully")
}
