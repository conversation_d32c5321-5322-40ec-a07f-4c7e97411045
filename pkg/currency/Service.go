/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package currency

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/caarlos0/env"
	"go.uber.org/zap"
)

// CurrencyConfig holds configuration for the currency service
type CurrencyConfig struct {
	// OpenExchangeRates API configuration
	APIKey  string `env:"OPENEXCHANGERATES_API_KEY" envDefault:""`
	BaseURL string `env:"OPENEXCHANGERATES_BASE_URL" envDefault:"https://openexchangerates.org/api"`

	// Cache configuration
	CacheTTLHours int `env:"CURRENCY_CACHE_TTL_HOURS" envDefault:"24"`

	// Default base currency
	BaseCurrency string `env:"CURRENCY_BASE_CURRENCY" envDefault:"USD"`

	// Cron job configuration for refreshing rates
	RefreshCronSchedule string `env:"CURRENCY_REFRESH_CRON" envDefault:"0 0 * * *"` // Daily at midnight

	// HTTP client timeout
	HTTPTimeoutSeconds int `env:"CURRENCY_HTTP_TIMEOUT_SECONDS" envDefault:"30"`
}

// ExchangeRatesResponse represents the response from OpenExchangeRates API
type ExchangeRatesResponse struct {
	Disclaimer string             `json:"disclaimer"`
	License    string             `json:"license"`
	Timestamp  int64              `json:"timestamp"`
	Base       string             `json:"base"`
	Rates      map[string]float64 `json:"rates"`
}

// ConversionRequest represents a currency conversion request
type ConversionRequest struct {
	From   string  `json:"from"`
	To     string  `json:"to"`
	Amount float64 `json:"amount"`
}

// ConversionResponse represents a currency conversion response
type ConversionResponse struct {
	From      string    `json:"from"`
	To        string    `json:"to"`
	Amount    float64   `json:"amount"`
	Result    float64   `json:"result"`
	Rate      float64   `json:"rate"`
	Timestamp time.Time `json:"timestamp"`
}

// CachedRates represents cached exchange rates with expiration
type CachedRates struct {
	Rates     map[string]float64 `json:"rates"`
	Base      string             `json:"base"`
	Timestamp time.Time          `json:"timestamp"`
	ExpiresAt time.Time          `json:"expires_at"`
}

// CurrencyService defines the interface for currency operations
type CurrencyService interface {
	// GetExchangeRates returns the latest exchange rates for the base currency
	GetExchangeRates(ctx context.Context, base string) (*ExchangeRatesResponse, error)

	// ConvertCurrency converts an amount from one currency to another
	ConvertCurrency(ctx context.Context, request *ConversionRequest) (*ConversionResponse, error)

	// GetSupportedCurrencies returns a list of supported currency codes
	GetSupportedCurrencies(ctx context.Context) ([]string, error)

	// RefreshRates manually refreshes the cached exchange rates
	RefreshRates(ctx context.Context) error

	// GetCacheStatus returns information about the cache status
	GetCacheStatus() map[string]interface{}
}

// CurrencyServiceImpl implements the CurrencyService interface
type CurrencyServiceImpl struct {
	config     *CurrencyConfig
	logger     *zap.SugaredLogger
	httpClient *http.Client
	cache      *sync.Map // map[string]*CachedRates
	cacheMutex sync.RWMutex
}

// NewCurrencyConfig creates a new currency configuration from environment variables
func NewCurrencyConfig(logger *zap.SugaredLogger) (*CurrencyConfig, error) {
	cfg := &CurrencyConfig{}
	err := env.Parse(cfg)
	if err != nil {
		logger.Errorw("error parsing currency config", "err", err)
		return nil, err
	}

	// Validate required configuration
	if cfg.APIKey == "" {
		logger.Warn("OpenExchangeRates API key not provided, service will have limited functionality")
	}

	logger.Infow("currency config loaded",
		"baseURL", cfg.BaseURL,
		"cacheTTL", cfg.CacheTTLHours,
		"baseCurrency", cfg.BaseCurrency,
		"refreshCron", cfg.RefreshCronSchedule,
		"httpTimeout", cfg.HTTPTimeoutSeconds)

	return cfg, nil
}

// NewCurrencyServiceImpl creates a new instance of CurrencyServiceImpl
func NewCurrencyServiceImpl(config *CurrencyConfig, logger *zap.SugaredLogger) *CurrencyServiceImpl {
	httpClient := &http.Client{
		Timeout: time.Duration(config.HTTPTimeoutSeconds) * time.Second,
	}

	service := &CurrencyServiceImpl{
		config:     config,
		logger:     logger,
		httpClient: httpClient,
		cache:      &sync.Map{},
	}

	// Initialize cache with default base currency
	go func() {
		ctx := context.Background()
		if err := service.RefreshRates(ctx); err != nil {
			logger.Errorw("failed to initialize currency cache", "err", err)
		}
	}()

	return service
}

// GetExchangeRates returns the latest exchange rates for the specified base currency
func (s *CurrencyServiceImpl) GetExchangeRates(ctx context.Context, base string) (*ExchangeRatesResponse, error) {
	if base == "" {
		base = s.config.BaseCurrency
	}

	// Try to get from cache first
	if cachedRates := s.getCachedRates(base); cachedRates != nil {
		s.logger.Debugw("returning cached exchange rates", "base", base, "cacheExpiry", cachedRates.ExpiresAt)
		return &ExchangeRatesResponse{
			Timestamp: cachedRates.Timestamp.Unix(),
			Base:      cachedRates.Base,
			Rates:     cachedRates.Rates,
		}, nil
	}

	// Fetch from API if not in cache or expired
	rates, err := s.fetchRatesFromAPI(ctx, base)
	if err != nil {
		s.logger.Errorw("failed to fetch rates from API", "base", base, "err", err)

		// Try to return stale cache data if available
		if staleRates := s.getStaleRates(base); staleRates != nil {
			s.logger.Warnw("returning stale cached rates due to API failure", "base", base)
			return &ExchangeRatesResponse{
				Timestamp: staleRates.Timestamp.Unix(),
				Base:      staleRates.Base,
				Rates:     staleRates.Rates,
			}, nil
		}

		return nil, fmt.Errorf("failed to fetch exchange rates and no cached data available: %w", err)
	}

	// Cache the new rates
	s.cacheRates(base, rates)

	return rates, nil
}

// ConvertCurrency converts an amount from one currency to another
func (s *CurrencyServiceImpl) ConvertCurrency(ctx context.Context, request *ConversionRequest) (*ConversionResponse, error) {
	if request.From == "" || request.To == "" {
		return nil, fmt.Errorf("both 'from' and 'to' currencies must be specified")
	}

	if request.Amount < 0 {
		return nil, fmt.Errorf("amount must be non-negative")
	}

	// If converting to the same currency, return the same amount
	if request.From == request.To {
		return &ConversionResponse{
			From:      request.From,
			To:        request.To,
			Amount:    request.Amount,
			Result:    request.Amount,
			Rate:      1.0,
			Timestamp: time.Now(),
		}, nil
	}

	// Get exchange rates with USD as base (OpenExchangeRates free tier limitation)
	rates, err := s.GetExchangeRates(ctx, "USD")
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange rates: %w", err)
	}

	// Calculate conversion rate
	var conversionRate float64

	if request.From == "USD" {
		// Converting from USD to another currency
		toRate, exists := rates.Rates[request.To]
		if !exists {
			return nil, fmt.Errorf("currency '%s' not supported", request.To)
		}
		conversionRate = toRate
	} else if request.To == "USD" {
		// Converting from another currency to USD
		fromRate, exists := rates.Rates[request.From]
		if !exists {
			return nil, fmt.Errorf("currency '%s' not supported", request.From)
		}
		conversionRate = 1.0 / fromRate
	} else {
		// Converting between two non-USD currencies
		fromRate, fromExists := rates.Rates[request.From]
		toRate, toExists := rates.Rates[request.To]

		if !fromExists {
			return nil, fmt.Errorf("currency '%s' not supported", request.From)
		}
		if !toExists {
			return nil, fmt.Errorf("currency '%s' not supported", request.To)
		}

		// Convert via USD: from -> USD -> to
		conversionRate = toRate / fromRate
	}

	result := request.Amount * conversionRate

	return &ConversionResponse{
		From:      request.From,
		To:        request.To,
		Amount:    request.Amount,
		Result:    result,
		Rate:      conversionRate,
		Timestamp: time.Now(),
	}, nil
}

// GetSupportedCurrencies returns a list of supported currency codes
func (s *CurrencyServiceImpl) GetSupportedCurrencies(ctx context.Context) ([]string, error) {
	rates, err := s.GetExchangeRates(ctx, "USD")
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange rates: %w", err)
	}

	currencies := make([]string, 0, len(rates.Rates)+1)
	currencies = append(currencies, "USD") // Base currency

	for currency := range rates.Rates {
		currencies = append(currencies, currency)
	}

	return currencies, nil
}

// RefreshRates manually refreshes the cached exchange rates
func (s *CurrencyServiceImpl) RefreshRates(ctx context.Context) error {
	s.logger.Info("refreshing currency exchange rates")

	// Refresh rates for the default base currency
	_, err := s.fetchRatesFromAPI(ctx, s.config.BaseCurrency)
	if err != nil {
		s.logger.Errorw("failed to refresh rates", "base", s.config.BaseCurrency, "err", err)
		return err
	}

	s.logger.Info("currency exchange rates refreshed successfully")
	return nil
}

// GetCacheStatus returns information about the cache status
func (s *CurrencyServiceImpl) GetCacheStatus() map[string]interface{} {
	status := make(map[string]interface{})

	cacheCount := 0
	var oldestEntry, newestEntry time.Time

	s.cache.Range(func(key, value interface{}) bool {
		cacheCount++
		if cached, ok := value.(*CachedRates); ok {
			if oldestEntry.IsZero() || cached.Timestamp.Before(oldestEntry) {
				oldestEntry = cached.Timestamp
			}
			if newestEntry.IsZero() || cached.Timestamp.After(newestEntry) {
				newestEntry = cached.Timestamp
			}
		}
		return true
	})

	status["cache_entries"] = cacheCount
	status["cache_ttl_hours"] = s.config.CacheTTLHours

	if !oldestEntry.IsZero() {
		status["oldest_entry"] = oldestEntry
		status["newest_entry"] = newestEntry
	}

	return status
}

// fetchRatesFromAPI fetches exchange rates from the OpenExchangeRates API
func (s *CurrencyServiceImpl) fetchRatesFromAPI(ctx context.Context, base string) (*ExchangeRatesResponse, error) {
	if s.config.APIKey == "" {
		return nil, fmt.Errorf("OpenExchangeRates API key not configured")
	}

	url := fmt.Sprintf("%s/latest.json?app_id=%s", s.config.BaseURL, s.config.APIKey)
	if base != "USD" {
		url += "&base=" + base
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "Devtron-Central-API/1.0")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d", resp.StatusCode)
	}

	var rates ExchangeRatesResponse
	if err := json.NewDecoder(resp.Body).Decode(&rates); err != nil {
		return nil, fmt.Errorf("failed to decode API response: %w", err)
	}

	s.logger.Debugw("fetched rates from API",
		"base", rates.Base,
		"timestamp", rates.Timestamp,
		"rateCount", len(rates.Rates))

	return &rates, nil
}

// getCachedRates retrieves valid (non-expired) cached rates
func (s *CurrencyServiceImpl) getCachedRates(base string) *CachedRates {
	value, exists := s.cache.Load(base)
	if !exists {
		return nil
	}

	cached, ok := value.(*CachedRates)
	if !ok {
		return nil
	}

	// Check if cache is still valid
	if time.Now().After(cached.ExpiresAt) {
		s.logger.Debugw("cached rates expired", "base", base, "expiredAt", cached.ExpiresAt)
		return nil
	}

	return cached
}

// getStaleRates retrieves cached rates even if expired (for fallback purposes)
func (s *CurrencyServiceImpl) getStaleRates(base string) *CachedRates {
	value, exists := s.cache.Load(base)
	if !exists {
		return nil
	}

	cached, ok := value.(*CachedRates)
	if !ok {
		return nil
	}

	return cached
}

// cacheRates stores exchange rates in the cache with TTL
func (s *CurrencyServiceImpl) cacheRates(base string, rates *ExchangeRatesResponse) {
	cached := &CachedRates{
		Rates:     rates.Rates,
		Base:      rates.Base,
		Timestamp: time.Unix(rates.Timestamp, 0),
		ExpiresAt: time.Now().Add(time.Duration(s.config.CacheTTLHours) * time.Hour),
	}

	s.cache.Store(base, cached)

	s.logger.Debugw("cached exchange rates",
		"base", base,
		"expiresAt", cached.ExpiresAt,
		"rateCount", len(cached.Rates))
}
