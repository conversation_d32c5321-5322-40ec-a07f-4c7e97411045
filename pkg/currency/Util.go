/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package currency

import (
	"context"
	"time"

	"go.uber.org/zap"
)

// CurrencyScheduler manages the cron job for refreshing currency rates
type CurrencyScheduler struct {
	service    CurrencyService
	logger     *zap.SugaredLogger
	stopChan   chan struct{}
	isRunning  bool
	cronTicker *time.Ticker
}

// NewCurrencyScheduler creates a new currency scheduler
func NewCurrencyScheduler(service CurrencyService, logger *zap.SugaredLogger) *CurrencyScheduler {
	return &CurrencyScheduler{
		service:   service,
		logger:    logger,
		stopChan:  make(chan struct{}),
		isRunning: false,
	}
}

// Start begins the cron job for refreshing currency rates
// For simplicity, we'll use a daily refresh at midnight UTC
func (s *CurrencyScheduler) Start() {
	if s.isRunning {
		s.logger.Warn("currency scheduler is already running")
		return
	}

	s.isRunning = true
	s.logger.Info("starting currency rate refresh scheduler")

	// Calculate time until next midnight UTC
	now := time.Now().UTC()
	nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, time.UTC)
	timeUntilMidnight := nextMidnight.Sub(now)

	// Start with initial delay until midnight, then every 24 hours
	go func() {
		// Wait until first midnight
		select {
		case <-time.After(timeUntilMidnight):
			s.refreshRates()
		case <-s.stopChan:
			return
		}

		// Then refresh every 24 hours
		s.cronTicker = time.NewTicker(24 * time.Hour)
		defer s.cronTicker.Stop()

		for {
			select {
			case <-s.cronTicker.C:
				s.refreshRates()
			case <-s.stopChan:
				s.logger.Info("currency scheduler stopped")
				return
			}
		}
	}()
}

// Stop stops the cron job
func (s *CurrencyScheduler) Stop() {
	if !s.isRunning {
		return
	}

	s.logger.Info("stopping currency rate refresh scheduler")
	close(s.stopChan)

	if s.cronTicker != nil {
		s.cronTicker.Stop()
	}

	s.isRunning = false
}

// refreshRates performs the actual rate refresh
func (s *CurrencyScheduler) refreshRates() {
	s.logger.Info("scheduled currency rate refresh started")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	if err := s.service.RefreshRates(ctx); err != nil {
		s.logger.Errorw("scheduled currency rate refresh failed", "err", err)
	} else {
		s.logger.Info("scheduled currency rate refresh completed successfully")
	}
}

// IsRunning returns whether the scheduler is currently running
func (s *CurrencyScheduler) IsRunning() bool {
	return s.isRunning
}

// ValidateCurrencyCode checks if a currency code is valid (3-letter ISO format)
func ValidateCurrencyCode(code string) bool {
	if len(code) != 3 {
		return false
	}

	// Check if all characters are uppercase letters
	for _, char := range code {
		if char < 'A' || char > 'Z' {
			return false
		}
	}

	return true
}

// FormatCurrency formats a currency amount to a reasonable number of decimal places
func FormatCurrency(amount float64) float64 {
	// Round to 6 decimal places for precision
	return float64(int(amount*1000000+0.5)) / 1000000
}

// GetCommonCurrencies returns a list of commonly used currency codes
func GetCommonCurrencies() []string {
	return []string{
		"USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD",
		"MXN", "SGD", "HKD", "NOK", "TRY", "RUB", "INR", "BRL", "ZAR", "KRW",
	}
}
