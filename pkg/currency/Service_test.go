/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package currency

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"go.uber.org/zap"
)

// MockHTTPClient for testing
type MockHTTPClient struct {
	DoFunc func(req *http.Request) (*http.Response, error)
}

func (m *MockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	return m.DoFunc(req)
}

func TestNewCurrencyConfig(t *testing.T) {
	logger := zap.NewNop().Sugar()
	
	config, err := NewCurrencyConfig(logger)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if config.BaseCurrency != "USD" {
		t.Errorf("Expected default base currency to be USD, got %s", config.BaseCurrency)
	}
	
	if config.CacheTTLHours != 24 {
		t.Errorf("Expected default cache TTL to be 24 hours, got %d", config.CacheTTLHours)
	}
}

func TestCurrencyServiceImpl_ConvertCurrency_SameCurrency(t *testing.T) {
	logger := zap.NewNop().Sugar()
	config := &CurrencyConfig{
		APIKey:             "test-key",
		BaseURL:            "https://test.com",
		CacheTTLHours:      24,
		BaseCurrency:       "USD",
		HTTPTimeoutSeconds: 30,
	}
	
	service := NewCurrencyServiceImpl(config, logger)
	
	request := &ConversionRequest{
		From:   "USD",
		To:     "USD",
		Amount: 100.0,
	}
	
	result, err := service.ConvertCurrency(context.Background(), request)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if result.Result != 100.0 {
		t.Errorf("Expected result to be 100.0, got %f", result.Result)
	}
	
	if result.Rate != 1.0 {
		t.Errorf("Expected rate to be 1.0, got %f", result.Rate)
	}
}

func TestCurrencyServiceImpl_ConvertCurrency_InvalidInput(t *testing.T) {
	logger := zap.NewNop().Sugar()
	config := &CurrencyConfig{
		APIKey:             "test-key",
		BaseURL:            "https://test.com",
		CacheTTLHours:      24,
		BaseCurrency:       "USD",
		HTTPTimeoutSeconds: 30,
	}
	
	service := NewCurrencyServiceImpl(config, logger)
	
	// Test empty currencies
	request := &ConversionRequest{
		From:   "",
		To:     "USD",
		Amount: 100.0,
	}
	
	_, err := service.ConvertCurrency(context.Background(), request)
	if err == nil {
		t.Error("Expected error for empty 'from' currency")
	}
	
	// Test negative amount
	request = &ConversionRequest{
		From:   "USD",
		To:     "EUR",
		Amount: -100.0,
	}
	
	_, err = service.ConvertCurrency(context.Background(), request)
	if err == nil {
		t.Error("Expected error for negative amount")
	}
}

func TestCurrencyServiceImpl_CacheRates(t *testing.T) {
	logger := zap.NewNop().Sugar()
	config := &CurrencyConfig{
		APIKey:             "test-key",
		BaseURL:            "https://test.com",
		CacheTTLHours:      24,
		BaseCurrency:       "USD",
		HTTPTimeoutSeconds: 30,
	}
	
	service := NewCurrencyServiceImpl(config, logger)
	
	// Create test rates
	rates := &ExchangeRatesResponse{
		Timestamp: time.Now().Unix(),
		Base:      "USD",
		Rates: map[string]float64{
			"EUR": 0.85,
			"GBP": 0.75,
		},
	}
	
	// Cache the rates
	service.cacheRates("USD", rates)
	
	// Retrieve cached rates
	cached := service.getCachedRates("USD")
	if cached == nil {
		t.Fatal("Expected cached rates to be available")
	}
	
	if cached.Base != "USD" {
		t.Errorf("Expected cached base to be USD, got %s", cached.Base)
	}
	
	if len(cached.Rates) != 2 {
		t.Errorf("Expected 2 cached rates, got %d", len(cached.Rates))
	}
	
	if cached.Rates["EUR"] != 0.85 {
		t.Errorf("Expected EUR rate to be 0.85, got %f", cached.Rates["EUR"])
	}
}

func TestCurrencyServiceImpl_GetCacheStatus(t *testing.T) {
	logger := zap.NewNop().Sugar()
	config := &CurrencyConfig{
		APIKey:             "test-key",
		BaseURL:            "https://test.com",
		CacheTTLHours:      24,
		BaseCurrency:       "USD",
		HTTPTimeoutSeconds: 30,
	}
	
	service := NewCurrencyServiceImpl(config, logger)
	
	// Initially, cache should be empty
	status := service.GetCacheStatus()
	if status["cache_entries"] != 0 {
		t.Errorf("Expected 0 cache entries, got %v", status["cache_entries"])
	}
	
	// Add some test data to cache
	rates := &ExchangeRatesResponse{
		Timestamp: time.Now().Unix(),
		Base:      "USD",
		Rates: map[string]float64{
			"EUR": 0.85,
		},
	}
	service.cacheRates("USD", rates)
	
	// Check status again
	status = service.GetCacheStatus()
	if status["cache_entries"] != 1 {
		t.Errorf("Expected 1 cache entry, got %v", status["cache_entries"])
	}
	
	if status["cache_ttl_hours"] != 24 {
		t.Errorf("Expected cache TTL to be 24, got %v", status["cache_ttl_hours"])
	}
}

func TestCurrencyServiceImpl_FetchRatesFromAPI_Success(t *testing.T) {
	logger := zap.NewNop().Sugar()
	
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := ExchangeRatesResponse{
			Timestamp: time.Now().Unix(),
			Base:      "USD",
			Rates: map[string]float64{
				"EUR": 0.85,
				"GBP": 0.75,
			},
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	config := &CurrencyConfig{
		APIKey:             "test-key",
		BaseURL:            server.URL,
		CacheTTLHours:      24,
		BaseCurrency:       "USD",
		HTTPTimeoutSeconds: 30,
	}
	
	service := NewCurrencyServiceImpl(config, logger)
	
	rates, err := service.fetchRatesFromAPI(context.Background(), "USD")
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	
	if rates.Base != "USD" {
		t.Errorf("Expected base to be USD, got %s", rates.Base)
	}
	
	if len(rates.Rates) != 2 {
		t.Errorf("Expected 2 rates, got %d", len(rates.Rates))
	}
	
	if rates.Rates["EUR"] != 0.85 {
		t.Errorf("Expected EUR rate to be 0.85, got %f", rates.Rates["EUR"])
	}
}

func TestCurrencyServiceImpl_FetchRatesFromAPI_NoAPIKey(t *testing.T) {
	logger := zap.NewNop().Sugar()
	config := &CurrencyConfig{
		APIKey:             "", // No API key
		BaseURL:            "https://test.com",
		CacheTTLHours:      24,
		BaseCurrency:       "USD",
		HTTPTimeoutSeconds: 30,
	}
	
	service := NewCurrencyServiceImpl(config, logger)
	
	_, err := service.fetchRatesFromAPI(context.Background(), "USD")
	if err == nil {
		t.Error("Expected error when API key is not configured")
	}
}

func TestValidateCurrencyCode(t *testing.T) {
	tests := []struct {
		code     string
		expected bool
	}{
		{"USD", true},
		{"EUR", true},
		{"GBP", true},
		{"usd", false}, // lowercase
		{"US", false},  // too short
		{"USDD", false}, // too long
		{"123", false}, // numbers
		{"U$D", false}, // special characters
		{"", false},    // empty
	}
	
	for _, test := range tests {
		result := ValidateCurrencyCode(test.code)
		if result != test.expected {
			t.Errorf("ValidateCurrencyCode(%s) = %v, expected %v", test.code, result, test.expected)
		}
	}
}

func TestFormatCurrency(t *testing.T) {
	tests := []struct {
		input    float64
		expected float64
	}{
		{1.234567890, 1.234568},
		{0.123456789, 0.123457},
		{100.0, 100.0},
		{0.0, 0.0},
		{1.999999, 2.0},
	}
	
	for _, test := range tests {
		result := FormatCurrency(test.input)
		if result != test.expected {
			t.Errorf("FormatCurrency(%f) = %f, expected %f", test.input, result, test.expected)
		}
	}
}

func TestGetCommonCurrencies(t *testing.T) {
	currencies := GetCommonCurrencies()
	
	if len(currencies) == 0 {
		t.Error("Expected common currencies list to not be empty")
	}
	
	// Check if USD is in the list
	found := false
	for _, currency := range currencies {
		if currency == "USD" {
			found = true
			break
		}
	}
	
	if !found {
		t.Error("Expected USD to be in common currencies list")
	}
}
