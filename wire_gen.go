// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/devtron-labs/central-api/api"
	"github.com/devtron-labs/central-api/client"
	"github.com/devtron-labs/central-api/internal/logger"
	"github.com/devtron-labs/central-api/pkg"
	"github.com/devtron-labs/common-lib/blob-storage"
)

// Injectors from Wire.go:

func InitializeApp() (*App, error) {
	sugaredLogger, err := logger.NewSugardLogger()
	if err != nil {
		return nil, err
	}
	gitHubClient, err := util.NewGitHubClient(sugaredLogger)
	if err != nil {
		return nil, err
	}
	moduleConfig, err := util.NewModuleConfig(sugaredLogger)
	if err != nil {
		return nil, err
	}
	blobConfigVariables, err := util.NewBlobConfig(sugaredLogger)
	if err != nil {
		return nil, err
	}
	blobStorageServiceImpl := blob_storage.NewBlobStorageServiceImpl(sugaredLogger)
	releaseNoteServiceImpl, err := pkg.NewReleaseNoteServiceImpl(sugaredLogger, gitHubClient, moduleConfig, blobConfigVariables, blobStorageServiceImpl)
	if err != nil {
		return nil, err
	}
	webhookSecretValidatorImpl := pkg.NewWebhookSecretValidatorImpl(sugaredLogger, gitHubClient)
	ciBuildMetadataServiceImpl := pkg.NewCiBuildMetadataServiceImpl(sugaredLogger)
	restHandlerImpl := api.NewRestHandlerImpl(sugaredLogger, releaseNoteServiceImpl, webhookSecretValidatorImpl, gitHubClient, ciBuildMetadataServiceImpl)
	muxRouter := api.NewMuxRouter(sugaredLogger, restHandlerImpl)
	app := NewApp(muxRouter, sugaredLogger)
	return app, nil
}
